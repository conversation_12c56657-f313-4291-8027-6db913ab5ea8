<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class FacturaDetalle
{
    // --- Atributos ---
    private ?int    $id             = null;
    private ?int    $id_factura     = null;
    private ?string $descripcion    = null;
    private ?int    $cantidad       = null;
    private ?float  $valor_unitario = null;
    private ?float  $valor_total    = null;

    /**
     * Constructor: Inicializa las propiedades del objeto FacturaDetalle.
     */
    public function __construct()
    {
        $this->id             = 0;
        $this->id_factura     = null;
        $this->descripcion    = null;
        $this->cantidad       = 1;
        $this->valor_unitario = 0.0; // Default to 0 as per lunex patterns
        $this->valor_total    = 0.0; // Default to 0 as per lunex patterns
    }

    /**
     * Método estático para construir un objeto FacturaDetalle desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del detalle de factura.
     *
     * @return self Instancia de FacturaDetalle.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                  = new self();
            $objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_factura      = isset($resultado['id_factura']) ? (int)$resultado['id_factura'] : null;
            $objeto->descripcion     = $resultado['descripcion'] ?? null;
            $objeto->cantidad        = isset($resultado['cantidad']) ? (int)$resultado['cantidad'] : 1;
            $objeto->valor_unitario  = isset($resultado['valor_unitario']) ? (float)$resultado['valor_unitario'] : 0.0;
            $objeto->valor_total     = isset($resultado['valor_total']) ? (float)$resultado['valor_total'] : 0.0;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir FacturaDetalle: " . $e->getMessage());
        }
    }

    /**
     * Obtiene un detalle de factura por su ID.
     *
     * @param int $id       ID del detalle de factura.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto FacturaDetalle o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM facturas_detalle
            WHERE
                id = :detalle_id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":detalle_id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener FacturaDetalle (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene todos los detalles de una factura específica.
     *
     * @param int $id_factura ID de la factura.
     * @param PDO $conexion   Conexión PDO.
     *
     * @return array Array de objetos FacturaDetalle.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_factura(int $id_factura, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM facturas_detalle
            WHERE
                id_factura = :factura_id
            ORDER BY
                id ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":factura_id", $id_factura, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener detalles de factura (ID Factura: $id_factura): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de todos los detalles de facturas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos FacturaDetalle.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                fd.*,
                f.fecha as factura_fecha,
                p.nombre as proveedor_nombre
            FROM facturas_detalle fd
            LEFT JOIN facturas f ON fd.id_factura = f.id
            LEFT JOIN proveedores p ON f.id_proveedor = p.id
            ORDER BY
                f.fecha DESC, fd.id ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de FacturaDetalle: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo detalle de factura en la base de datos a partir de un objeto FacturaDetalle.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo detalle creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        if ($this->getId_factura() === null || $this->getId_factura() <= 0) {
            throw new Exception("ID de factura es requerido en el objeto FacturaDetalle para crearlo.");
        }

        if (empty($this->getDescripcion())) {
            throw new Exception("Descripción es requerida en el objeto FacturaDetalle para crearlo.");
        }

        if ($this->getCantidad() === null || $this->getCantidad() <= 0) {
            throw new Exception("Cantidad debe ser mayor que cero en el objeto FacturaDetalle para crearlo.");
        }

        try {
            // Calcular valor total si no está establecido
            if ($this->valor_total === null || $this->valor_total === 0.0) {
                $this->calcularValorTotal();
            }

            $query = <<<SQL
            INSERT INTO facturas_detalle (
                 id_factura
                ,descripcion
                ,cantidad
                ,valor_unitario
                ,valor_total
            ) VALUES (
                 :id_factura
                ,:descripcion
                ,:cantidad
                ,:valor_unitario
                ,:valor_total
            )
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':id_factura', $this->getId_factura(), PDO::PARAM_INT);
            $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
            $statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
            $statement->bindValue(':valor_unitario', $this->getValor_unitario(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            if ($e->getCode() == 23000 || $e->getCode() == 1062) {
                throw new Exception("Error al crear detalle de factura: Violación de constraint de integridad.");
            } else {
                throw new Exception("Error de base de datos al crear detalle de factura: " . $e->getMessage());
            }
        } catch (Exception $e) {
            throw new Exception("Error al crear detalle de factura: " . $e->getMessage());
        }
    }

    /**
     * Modifica un detalle de factura existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    public function modificar(PDO $conexion): bool
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("ID válido es requerido en el objeto FacturaDetalle para modificarlo.");
        }

        if ($this->getId_factura() === null || $this->getId_factura() <= 0) {
            throw new Exception("ID de factura es requerido en el objeto FacturaDetalle para modificarlo.");
        }

        if (empty($this->getDescripcion())) {
            throw new Exception("Descripción es requerida en el objeto FacturaDetalle para modificarlo.");
        }

        if ($this->getCantidad() === null || $this->getCantidad() <= 0) {
            throw new Exception("Cantidad debe ser mayor que cero en el objeto FacturaDetalle para modificarlo.");
        }

        try {
            // Recalcular valor total
            $this->calcularValorTotal();

            $query = <<<SQL
            UPDATE facturas_detalle SET
                id_factura = :id_factura,
                descripcion = :descripcion,
                cantidad = :cantidad,
                valor_unitario = :valor_unitario,
                valor_total = :valor_total
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_factura', $this->getId_factura(), PDO::PARAM_INT);
            $statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
            $statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
            $statement->bindValue(':valor_unitario', $this->getValor_unitario(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar detalle de factura (ID: " . $this->getId() . "): " . $e->getMessage());
        }
    }

    /**
     * Elimina un detalle de factura por su ID.
     *
     * @param int $id       ID del detalle de factura a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM facturas_detalle
            WHERE
                id = :detalle_id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':detalle_id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar detalle de factura (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId_factura(): ?int
    {
        return $this->id_factura;
    }

    public function setId_factura(?int $id_factura): self
    {
        $this->id_factura = $id_factura;
        return $this;
    }

    public function getDescripcion(): ?string
    {
        return $this->descripcion;
    }

    public function setDescripcion(?string $descripcion): self
    {
        $this->descripcion = $descripcion;
        return $this;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(?int $cantidad): self
    {
        $this->cantidad = $cantidad;
        return $this;
    }

    public function getValor_unitario(): ?float
    {
        return $this->valor_unitario;
    }

    public function setValor_unitario(?float $valor_unitario): self
    {
        $this->valor_unitario = $valor_unitario ?? 0.0;
        return $this;
    }

    public function getValor_total(): ?float
    {
        return $this->valor_total;
    }

    public function setValor_total(?float $valor_total): self
    {
        $this->valor_total = $valor_total ?? 0.0;
        return $this;
    }

    // --- Métodos de relación y cálculo ---

    /**
     * Obtiene el objeto Factura asociado a este detalle.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return Factura|null Objeto Factura o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public function getFactura(PDO $conexion): ?Factura
    {
        if ($this->id_factura === null) {
            return null;
        }

        return Factura::get($this->id_factura, $conexion);
    }

    /**
     * Calcula el valor total basado en cantidad y valor unitario.
     *
     * @return self Para permitir method chaining.
     */
    public function calcularValorTotal(): self
    {
        $cantidad = $this->cantidad ?? 0;
        $valorUnitario = $this->valor_unitario ?? 0.0;
        $this->valor_total = $cantidad * $valorUnitario;
        return $this;
    }

    /**
     * Valida que los datos del detalle sean consistentes.
     *
     * @return bool True si los datos son válidos.
     * @throws Exception Si los datos no son válidos.
     */
    public function validar(): bool
    {
        if ($this->cantidad === null || $this->cantidad <= 0) {
            throw new Exception("La cantidad debe ser mayor que cero.");
        }

        if ($this->valor_unitario === null || $this->valor_unitario < 0) {
            throw new Exception("El valor unitario no puede ser negativo.");
        }

        if (empty(trim($this->descripcion))) {
            throw new Exception("La descripción no puede estar vacía.");
        }

        // Verificar que el valor total calculado coincida con el almacenado
        $valorTotalCalculado = $this->cantidad * $this->valor_unitario;
        $diferencia = abs($valorTotalCalculado - ($this->valor_total ?? 0.0));

        if ($diferencia > 0.01) { // Tolerancia para errores de punto flotante
            throw new Exception("El valor total no coincide con el cálculo (cantidad × valor unitario).");
        }

        return true;
    }
}
