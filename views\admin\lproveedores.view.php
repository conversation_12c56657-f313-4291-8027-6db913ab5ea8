<?php
#region region DOCS

/** @var Proveedor[] $proveedores */
/** @var string $filtro_nombre */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Proveedor;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Proveedores</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="Gestión de proveedores" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <!-- Estilos adicionales específicos para esta página -->
    <style>
        /* Toast notification styling */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1055;
        }

        .toast {
            min-width: 300px;
        }

        .toast-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .toast-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Gestión de Proveedores</h4>
                <p class="mb-0 text-muted">Administra los proveedores del sistema</p>
            </div>
            <div class="ms-auto">
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#crearProveedorModal">
                    <i class="fa fa-plus-circle fa-fw me-1"></i> Nuevo Proveedor
                </button>
            </div>
        </div>

        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region region FILTERS ?>
        <div class="panel panel-inverse mt-3 no-border-radious">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">Filtros</h4>
            </div>
            <div class="panel-body">
                <form method="GET" action="listado-proveedores" class="row g-3">
                    <div class="col-md-4">
                        <label for="nombre" class="form-label">Nombre del Proveedor</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" 
                               value="<?php echo htmlspecialchars($filtro_nombre); ?>" 
                               placeholder="Buscar por nombre...">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fa fa-search"></i> Filtrar
                        </button>
                        <a href="listado-proveedores" class="btn btn-secondary">
                            <i class="fa fa-times"></i> Limpiar
                        </a>
                    </div>
                </form>
            </div>
        </div>
        <?php #endregion FILTERS ?>

        <?php #region region PANEL PROVEEDORES ?>
        <div class="panel panel-inverse mt-3 no-border-radious">
            <div class="panel-heading no-border-radious">
                <h4 class="panel-title">
                    Listado de Proveedores Activos
                    <?php if (!empty($filtro_nombre)): ?>
                        <small class="text-muted">(Filtrado por: "<?php echo htmlspecialchars($filtro_nombre); ?>")</small>
                    <?php endif; ?>
                </h4>
            </div>
            <!-- BEGIN PANEL body -->
            <div class="p-1 table-nowrap" style="overflow: auto">
                <?php #region region TABLE PROVEEDORES ?>
                <table class="table table-hover table-sm">
                    <thead>
                    <tr>
                        <th>Acciones</th>
                        <th>Nombre</th>
                        <th>NIT</th>
                    </tr>
                    </thead>
                    <tbody class="fs-12px" id="proveedor-table-body">
                    <?php foreach ($proveedores as $proveedor): ?>
                        <tr data-proveedor-id="<?php echo $proveedor->getId(); ?>">
                            <td>
                                <?php // Edit Button ?>
                                <button type="button" class="btn btn-xs btn-primary me-1 btn-editar-proveedor"
                                        title="Editar Proveedor"
                                        data-proveedorid="<?php echo $proveedor->getId(); ?>"
                                        data-nombre="<?php echo htmlspecialchars($proveedor->getNombre() ?? ''); ?>"
                                        data-nit="<?php echo htmlspecialchars($proveedor->getNit() ?? ''); ?>">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <?php // Deactivate Button ?>
                                <button type="button" class="btn btn-xs btn-danger btn-desactivar-proveedor"
                                        title="Desactivar"
                                        data-proveedorid="<?php echo $proveedor->getId(); ?>"
                                        data-nombre="<?php echo htmlspecialchars($proveedor->getNombre() ?? ''); ?>">
                                    <i class="fa fa-trash-alt"></i>
                                </button>
                            </td>
                            <td class="proveedor-nombre-cell"><?php echo htmlspecialchars($proveedor->getNombre()); ?></td>
                            <td class="proveedor-nit-cell"><?php echo htmlspecialchars($proveedor->getNit()); ?></td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
                <div id="empty-state" class="text-center py-4" style="display: none;">
                    <p class="text-muted">No se encontraron proveedores activos.</p>
                </div>
                <?php #endregion TABLE PROVEEDORES ?>
            </div>
            <!-- END PANEL body -->
        </div>
        <?php #endregion PANEL PROVEEDORES ?>

    </div>
    <!-- END #content -->

    <!-- Toast notification container -->
    <div class="toast-container" id="toast-container"></div>

    <?php #region region Crear Proveedor Modal ?>
    <div class="modal fade" id="crearProveedorModal" tabindex="-1" aria-labelledby="crearProveedorModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="crearProveedorModalLabel">Crear Nuevo Proveedor</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="crear-proveedor-form">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="crear_nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="crear_nombre" name="nombre" required>
                        </div>
                        <div class="mb-3">
                            <label for="crear_nit" class="form-label">NIT <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="crear_nit" name="nit" required>
                        </div>
                        <div id="crear-proveedor-feedback"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success">Crear Proveedor</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion Crear Proveedor Modal ?>

    <?php #region region Editar Proveedor Modal ?>
    <div class="modal fade" id="editarProveedorModal" tabindex="-1" aria-labelledby="editarProveedorModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editarProveedorModalLabel">Editar Proveedor</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editar-proveedor-form">
                    <input type="hidden" id="editar_proveedor_id" name="proveedorId">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editar_nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editar_nombre" name="nombre" required>
                        </div>
                        <div class="mb-3">
                            <label for="editar_nit" class="form-label">NIT <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editar_nit" name="nit" required>
                        </div>
                        <div id="editar-proveedor-feedback"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Actualizar Proveedor</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion Editar Proveedor Modal ?>

    <?php #region region Hidden Form for Deactivation ?>
    <form id="deactivate-proveedor-form" method="POST" action="listado-proveedores" style="display: none;">
        <input type="hidden" name="action" value="desactivar">
        <input type="hidden" name="proveedorId" id="deactivate-proveedor-id">
    </form>
    <?php #endregion Hidden Form ?>

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log("Proveedores list page loaded.");
        
        // Get DOM elements
        const tableBody                  = document.getElementById('proveedor-table-body');
        const deactivateProveedorForm    = document.getElementById('deactivate-proveedor-form');
        const deactivateProveedorIdInput = document.getElementById('deactivate-proveedor-id');
        const crearProveedorModal        = new bootstrap.Modal(document.getElementById('crearProveedorModal'));
        const editarProveedorModal       = new bootstrap.Modal(document.getElementById('editarProveedorModal'));
        const crearProveedorForm         = document.getElementById('crear-proveedor-form');
        const editarProveedorForm        = document.getElementById('editar-proveedor-form');

        // Initialize empty state on page load
        updateEmptyState();

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const successAlert = document.getElementById('success-alert');
            const errorAlert = document.getElementById('error-alert');

            if (successAlert && successAlert.classList.contains('show')) {
                const bsAlert = new bootstrap.Alert(successAlert);
                bsAlert.close();
            }

            if (errorAlert && errorAlert.classList.contains('show')) {
                const bsAlert = new bootstrap.Alert(errorAlert);
                bsAlert.close();
            }
        }, 5000);

        // --- Table Event Handlers ---
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const editButton = event.target.closest('.btn-editar-proveedor');
                const deactivateButton = event.target.closest('.btn-desactivar-proveedor');

                // --- Handle Edit Click ---
                if (editButton) {
                    event.preventDefault();
                    const proveedorId = editButton.dataset.proveedorid;
                    const nombre = editButton.dataset.nombre;
                    const nit = editButton.dataset.nit;

                    // Populate edit modal
                    document.getElementById('editar_proveedor_id').value = proveedorId;
                    document.getElementById('editar_nombre').value = nombre;
                    document.getElementById('editar_nit').value = nit;

                    // Clear previous feedback
                    document.getElementById('editar-proveedor-feedback').innerHTML = '';

                    // Show modal
                    editarProveedorModal.show();
                }

                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault();
                    const proveedorId = deactivateButton.dataset.proveedorid;
                    const nombre = deactivateButton.dataset.nombre;

                    // Confirm before deactivating using SweetAlert
                    swal({
                        title: "Confirmar Desactivación",
                        text: `¿Está seguro que desea desactivar el proveedor "${nombre}"?`,
                        icon: "warning",
                        buttons: {
                            cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                    }).then((willDeactivate) => {
                        if (willDeactivate) {
                            deactivateProveedorIdInput.value = proveedorId;
                            deactivateProveedorForm.submit();
                        }
                    });
                }
            });
        }

        // --- Create Provider Form Submission ---
        if (crearProveedorForm) {
            crearProveedorForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const formData = new FormData(crearProveedorForm);
                formData.append('action', 'crear');

                const feedback = document.getElementById('crear-proveedor-feedback');
                feedback.innerHTML = '<div class="alert alert-info">Creando proveedor... <span class="spinner-border spinner-border-sm"></span></div>';

                fetch('listado-proveedores', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - close modal and show toast
                        crearProveedorModal.hide();
                        crearProveedorForm.reset();
                        showToastNotification(data.message, 'success');

                        // Add new row to table
                        addProveedorToTable(data.proveedor);
                        updateEmptyState();
                    } else {
                        // Error
                        feedback.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    feedback.innerHTML = '<div class="alert alert-danger">Error de comunicación. Inténtelo de nuevo.</div>';
                });
            });
        }

        // --- Edit Provider Form Submission ---
        if (editarProveedorForm) {
            editarProveedorForm.addEventListener('submit', function(event) {
                event.preventDefault();

                const formData = new FormData(editarProveedorForm);
                formData.append('action', 'modificar');

                const feedback = document.getElementById('editar-proveedor-feedback');
                feedback.innerHTML = '<div class="alert alert-info">Actualizando proveedor... <span class="spinner-border spinner-border-sm"></span></div>';

                fetch('listado-proveedores', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - close modal and show toast
                        editarProveedorModal.hide();
                        showToastNotification(data.message, 'success');

                        // Update row in table
                        updateProveedorInTable(data.proveedor);
                    } else {
                        // Error
                        feedback.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    feedback.innerHTML = '<div class="alert alert-danger">Error de comunicación. Inténtelo de nuevo.</div>';
                });
            });
        }

        // --- Toast Notification Functions ---
        function showToastNotification(message, type = 'success') {
            const toastContainer = document.getElementById('toast-container');
            if (!toastContainer) return;

            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div class="toast toast-${type}" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="4000">
                    <div class="toast-header">
                        <i class="fa ${type === 'success' ? 'fa-check-circle text-success' : 'fa-exclamation-circle text-danger'} me-2"></i>
                        <strong class="me-auto">${type === 'success' ? 'Éxito' : 'Error'}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // --- Table Update Functions ---
        function addProveedorToTable(proveedor) {
            const tableBody = document.getElementById('proveedor-table-body');
            if (!tableBody) return;

            const newRow = document.createElement('tr');
            newRow.setAttribute('data-proveedor-id', proveedor.id);
            newRow.innerHTML = `
                <td>
                    <button type="button" class="btn btn-xs btn-primary me-1 btn-editar-proveedor"
                            title="Editar Proveedor"
                            data-proveedorid="${proveedor.id}"
                            data-nombre="${escapeHtml(proveedor.nombre)}"
                            data-nit="${escapeHtml(proveedor.nit)}">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-xs btn-danger btn-desactivar-proveedor"
                            title="Desactivar"
                            data-proveedorid="${proveedor.id}"
                            data-nombre="${escapeHtml(proveedor.nombre)}">
                        <i class="fa fa-trash-alt"></i>
                    </button>
                </td>
                <td class="proveedor-nombre-cell">${escapeHtml(proveedor.nombre)}</td>
                <td class="proveedor-nit-cell">${escapeHtml(proveedor.nit)}</td>
            `;

            tableBody.appendChild(newRow);
        }

        function updateProveedorInTable(proveedor) {
            const row = document.querySelector(`tr[data-proveedor-id="${proveedor.id}"]`);
            if (!row) return;

            // Update data attributes in buttons
            const editButton = row.querySelector('.btn-editar-proveedor');
            const deleteButton = row.querySelector('.btn-desactivar-proveedor');

            if (editButton) {
                editButton.setAttribute('data-nombre', proveedor.nombre);
                editButton.setAttribute('data-nit', proveedor.nit);
            }

            if (deleteButton) {
                deleteButton.setAttribute('data-nombre', proveedor.nombre);
            }

            // Update table cells
            const nombreCell = row.querySelector('.proveedor-nombre-cell');
            const nitCell = row.querySelector('.proveedor-nit-cell');

            if (nombreCell) nombreCell.textContent = proveedor.nombre;
            if (nitCell) nitCell.textContent = proveedor.nit;
        }

        // --- Empty State Management ---
        function updateEmptyState() {
            const tableBody = document.getElementById('proveedor-table-body');
            const emptyState = document.getElementById('empty-state');

            if (!tableBody || !emptyState) return;

            const hasRows = tableBody.children.length > 0;

            if (hasRows) {
                emptyState.style.display = 'none';
            } else {
                emptyState.style.display = 'block';
            }
        }

        // --- Utility Functions ---
        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }

    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
