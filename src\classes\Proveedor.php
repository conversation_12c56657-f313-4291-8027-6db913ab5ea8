<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Proveedor
{
    // --- Atributos ---
    private ?int    $id     = null;
    private ?string $nombre = null;
    private ?string $nit    = null;
    private ?int    $estado = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Proveedor.
     */
    public function __construct()
    {
        $this->id     = 0; // O null si prefieres no usar 0 por defecto
        $this->nombre = null;
        $this->nit    = null;
        $this->estado = 1; // Estado activo por defecto
    }

    /**
     * Método estático para construir un objeto Proveedor desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del proveedor.
     * @return self Instancia de Proveedor.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto         = new self();
            $objeto->id     = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->nombre = $resultado['nombre'] ?? null;
            $objeto->nit    = $resultado['nit'] ?? null;
            $objeto->estado = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            return $objeto;
        } catch (Exception $e) {
            // Considera loggear el error aquí
            throw new Exception("Error al construir Proveedor: " . $e->getMessage());
        }
    }

    /**
     * Obtiene un proveedor por su ID.
     *
     * @param int $id ID del proveedor.
     * @param PDO $conexion Conexión PDO.
     * @return self|null Objeto Proveedor o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM proveedores
            WHERE
                id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Proveedor (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de proveedores activos.
     *
     * @param PDO $conexion Conexión PDO.
     * @return array Array de objetos Proveedor.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM proveedores
            WHERE estado = 1
            ORDER BY
                nombre
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Proveedores: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de proveedores activos con filtros opcionales.
     *
     * @param array $parametros Array con filtros opcionales (nombre).
     * @param PDO $conexion Conexión PDO.
     * @return array Array de objetos Proveedor.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list_filtered(array $parametros, PDO $conexion): array
    {
        try {
            $nombre = $parametros['nombre'] ?? null;

            $query = <<<SQL
            SELECT
                *
            FROM proveedores
            WHERE estado = 1
            SQL;

            $params = [];

            // Agregar filtro por nombre si se proporciona
            if (!empty($nombre)) {
                $query .= " AND nombre LIKE :nombre";
                $params[':nombre'] = '%' . trim($nombre) . '%';
            }

            $query .= " ORDER BY nombre";

            $statement = $conexion->prepare($query);

            // Bind parameters
            foreach ($params as $param => $value) {
                $statement->bindValue($param, $value, PDO::PARAM_STR);
            }

            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista filtrada de Proveedores: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos ---

    /**
     * Crea un nuevo proveedor en la base de datos a partir de un objeto Proveedor.
     * El objeto Proveedor debe estar poblado con nombre y nit.
     *
     * @param PDO $conexion Conexión PDO.
     * @return int|false El ID del nuevo proveedor creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    public function crear(PDO $conexion): int|false
    {
        if (empty($this->getNombre()) || empty($this->getNit())) {
            throw new Exception("Nombre y NIT son requeridos en el objeto Proveedor para crearlo.");
        }

        try {
            $query = <<<SQL
            INSERT INTO proveedores (
                 nombre
                ,nit
                ,estado
            ) VALUES (
                 :nombre
                ,:nit
                ,:estado
            )
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
            $statement->bindValue(':nit', $this->getNit(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }
        } catch (PDOException $e) {
            // Manejar errores específicos de DB (ej. NIT duplicado si hay constraint UNIQUE)
            if ($e->getCode() == 23000 || $e->getCode() == 1062) {
                throw new Exception("Error al crear proveedor: El NIT '" . $this->getNit() . "' podría ya existir o hay una violación de constraint.");
            } else {
                throw new Exception("Error de base de datos al crear proveedor: " . $e->getMessage());
            }
        }
    }

    /**
     * Modifica los datos de un proveedor existente.
     *
     * @param PDO $conexion Conexión PDO.
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    public function modificar(PDO $conexion): bool
    {
        if (empty($this->getNombre()) || empty($this->getNit())) {
            throw new Exception("Nombre y NIT son requeridos en el objeto Proveedor para modificarlo.");
        }

        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("ID válido es requerido en el objeto Proveedor para modificarlo.");
        }

        try {
            $query = <<<SQL
            UPDATE proveedores SET
                nombre = :nombre,
                nit = :nit
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':nombre', trim($this->getNombre()), PDO::PARAM_STR);
            $statement->bindValue(':nit', trim($this->getNit()), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            // Manejar errores específicos de DB (ej. NIT duplicado si hay constraint UNIQUE)
            if ($e->getCode() == 23000 || $e->getCode() == 1062) {
                throw new Exception("Error al modificar proveedor: El NIT '" . $this->getNit() . "' podría ya existir o hay una violación de constraint.");
            } else {
                throw new Exception("Error de base de datos al modificar proveedor (ID: " . $this->getId() . "): " . $e->getMessage());
            }
        }
    }

    /**
     * Desactiva un proveedor estableciendo su estado a 0.
     *
     * @param int $id       ID del proveedor a desactivar.
     * @param PDO $conexion Conexión PDO.
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            UPDATE proveedores SET
                estado = 0
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar proveedor (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;
        return $this;
    }

    public function getNit(): ?string
    {
        return $this->nit;
    }

    public function setNit(?string $nit): self
    {
        $this->nit = $nit;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        if ($estado !== null && !in_array($estado, [0, 1])) {
            throw new \InvalidArgumentException("El estado debe ser 0, 1 o null.");
        }
        $this->estado = $estado;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si el proveedor está activo.
     * @return bool
     */
    public function isActivo(): bool
    {
        return $this->estado === 1;
    }
}