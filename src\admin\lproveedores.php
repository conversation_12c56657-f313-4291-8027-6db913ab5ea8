<?php

// Iniciar sesión si es necesario
use App\classes\Proveedor;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lproveedores.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$proveedores     = []; // Initialize as an empty array
$error_display   = '';
$error_text      = '';
$success_display = '';
$success_text    = '';
$filtro_nombre   = '';
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle Filter
// Handle filter parameters
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	$filtro_nombre = filter_input(INPUT_GET, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS) ?? '';
}
#endregion Handle Filter

#region region Create provider
// --- Handle AJAX Request (Create Provider) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$nombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS);
	$nit    = filter_input(INPUT_POST, 'nit', FILTER_SANITIZE_SPECIAL_CHARS);
	
	// Validate required fields
	if (empty($nombre)) {
		$response['message'] = 'Error: El nombre es requerido.';
		echo json_encode($response);
		exit;
	}
	
	if (empty($nit)) {
		$response['message'] = 'Error: El NIT es requerido.';
		echo json_encode($response);
		exit;
	}
	
	try {
		// Create new provider object
		$proveedor = new Proveedor();
		$proveedor->setNombre($nombre);
		$proveedor->setNit($nit);
		
		// Save the provider
		$newId = $proveedor->crear($conexion);
		
		if ($newId) {
			$response = [
				'success'   => true,
				'message'   => 'Proveedor creado correctamente.',
				'proveedor' => [
					'id'     => $newId,
					'nombre' => $proveedor->getNombre(),
					'nit'    => $proveedor->getNit()
				]
			];
		} else {
			$response['message'] = 'Error: No se pudo crear el proveedor.';
		}
		
	} catch (Exception $e) {
		$response['message'] = 'Error: ' . $e->getMessage();
	}
	
	echo json_encode($response);
	exit;
}
#endregion Create provider

#region region Modify provider
// --- Handle AJAX Request (Modify Provider) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$proveedorId = filter_input(INPUT_POST, 'proveedorId', FILTER_VALIDATE_INT);
	$nombre      = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS);
	$nit         = filter_input(INPUT_POST, 'nit', FILTER_SANITIZE_SPECIAL_CHARS);
	
	// Validate required fields
	if (!$proveedorId) {
		$response['message'] = 'Error: ID de proveedor inválido.';
		echo json_encode($response);
		exit;
	}
	
	if (empty($nombre)) {
		$response['message'] = 'Error: El nombre es requerido.';
		echo json_encode($response);
		exit;
	}
	
	if (empty($nit)) {
		$response['message'] = 'Error: El NIT es requerido.';
		echo json_encode($response);
		exit;
	}
	
	try {
		// Get the existing provider
		$proveedor = Proveedor::get($proveedorId, $conexion);
		
		if (!$proveedor) {
			$response['message'] = 'Error: Proveedor no encontrado.';
			echo json_encode($response);
			exit;
		}
		
		// Update the provider properties
		$proveedor->setNombre($nombre);
		$proveedor->setNit($nit);
		
		// Save the changes
		$success = $proveedor->modificar($conexion);
		
		if ($success) {
			$response = [
				'success'   => true,
				'message'   => 'Proveedor actualizado correctamente.',
				'proveedor' => [
					'id'     => $proveedor->getId(),
					'nombre' => $proveedor->getNombre(),
					'nit'    => $proveedor->getNit()
				]
			];
		} else {
			$response['message'] = 'Error: No se pudo actualizar el proveedor.';
		}
		
	} catch (Exception $e) {
		$response['message'] = 'Error: ' . $e->getMessage();
	}
	
	echo json_encode($response);
	exit;
}
#endregion Modify provider

#region region Deactivate provider
// --- Handle POST Request (Deactivate Provider) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$proveedorIdToDeactivate = filter_input(INPUT_POST, 'proveedorId', FILTER_VALIDATE_INT);
	
	if ($proveedorIdToDeactivate) {
		try {
			$success = Proveedor::desactivar($proveedorIdToDeactivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Proveedor desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el proveedor.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar proveedor: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de proveedor inválido para desactivar.";
	}
	
	// Redirect back to the provider list page after processing
	header('Location: listado-proveedores');
	exit;
}
#endregion Deactivate provider

#region try
try {
	// Prepare filter parameters
	$parametros = [];
	if (!empty($filtro_nombre)) {
		$parametros['nombre'] = $filtro_nombre;
	}
	
	// Get filtered list of providers
	if (!empty($parametros)) {
		$proveedores = Proveedor::get_list_filtered($parametros, $conexion);
	} else {
		$proveedores = Proveedor::get_list($conexion);
	}
	
} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de proveedores.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de proveedores: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/admin/lproveedores.view.php';
