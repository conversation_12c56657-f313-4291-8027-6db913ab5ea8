<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class FacturaSoporte
{
    // --- Atributos ---
    private ?int    $id             = null;
    private ?int    $id_factura     = null;
    private ?string $nombre_archivo = null;

    /**
     * Constructor: Inicializa las propiedades del objeto FacturaSoporte.
     */
    public function __construct()
    {
        $this->id             = 0;
        $this->id_factura     = null;
        $this->nombre_archivo = null;
    }

    /**
     * Método estático para construir un objeto FacturaSoporte desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del soporte de factura.
     *
     * @return self Instancia de FacturaSoporte.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                  = new self();
            $objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_factura      = isset($resultado['id_factura']) ? (int)$resultado['id_factura'] : null;
            $objeto->nombre_archivo  = $resultado['nombre_archivo'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir FacturaSoporte: " . $e->getMessage());
        }
    }

    /**
     * Obtiene un soporte de factura por su ID.
     *
     * @param int $id       ID del soporte de factura.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto FacturaSoporte o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM facturas_soportes
            WHERE
                id = :soporte_id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":soporte_id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener FacturaSoporte (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene todos los soportes de una factura específica.
     *
     * @param int $id_factura ID de la factura.
     * @param PDO $conexion   Conexión PDO.
     *
     * @return array Array de objetos FacturaSoporte.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_factura(int $id_factura, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM facturas_soportes
            WHERE
                id_factura = :factura_id
            ORDER BY
                id ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":factura_id", $id_factura, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener soportes de factura (ID Factura: $id_factura): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de todos los soportes de facturas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos FacturaSoporte.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                fs.*,
                f.fecha as factura_fecha,
                p.nombre as proveedor_nombre
            FROM facturas_soportes fs
            LEFT JOIN facturas f ON fs.id_factura = f.id
            LEFT JOIN proveedores p ON f.id_proveedor = p.id
            ORDER BY
                f.fecha DESC, fs.id ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de FacturaSoporte: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo soporte de factura en la base de datos a partir de un objeto FacturaSoporte.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo soporte creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        if ($this->getId_factura() === null || $this->getId_factura() <= 0) {
            throw new Exception("ID de factura es requerido en el objeto FacturaSoporte para crearlo.");
        }

        if (empty($this->getNombre_archivo())) {
            throw new Exception("Nombre de archivo es requerido en el objeto FacturaSoporte para crearlo.");
        }

        try {
            $query = <<<SQL
            INSERT INTO facturas_soportes (
                 id_factura
                ,nombre_archivo
            ) VALUES (
                 :id_factura
                ,:nombre_archivo
            )
            SQL;

            $statement = $conexion->prepare($query);

            $statement->bindValue(':id_factura', $this->getId_factura(), PDO::PARAM_INT);
            $statement->bindValue(':nombre_archivo', $this->getNombre_archivo(), PDO::PARAM_STR);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            if ($e->getCode() == 23000 || $e->getCode() == 1062) {
                throw new Exception("Error al crear soporte de factura: Violación de constraint de integridad.");
            } else {
                throw new Exception("Error de base de datos al crear soporte de factura: " . $e->getMessage());
            }
        } catch (Exception $e) {
            throw new Exception("Error al crear soporte de factura: " . $e->getMessage());
        }
    }

    /**
     * Modifica un soporte de factura existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    public function modificar(PDO $conexion): bool
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("ID válido es requerido en el objeto FacturaSoporte para modificarlo.");
        }

        if ($this->getId_factura() === null || $this->getId_factura() <= 0) {
            throw new Exception("ID de factura es requerido en el objeto FacturaSoporte para modificarlo.");
        }

        if (empty($this->getNombre_archivo())) {
            throw new Exception("Nombre de archivo es requerido en el objeto FacturaSoporte para modificarlo.");
        }

        try {
            $query = <<<SQL
            UPDATE facturas_soportes SET
                id_factura = :id_factura,
                nombre_archivo = :nombre_archivo
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_factura', $this->getId_factura(), PDO::PARAM_INT);
            $statement->bindValue(':nombre_archivo', $this->getNombre_archivo(), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar soporte de factura (ID: " . $this->getId() . "): " . $e->getMessage());
        }
    }

    /**
     * Elimina un soporte de factura por su ID.
     *
     * @param int $id       ID del soporte de factura a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM facturas_soportes
            WHERE
                id = :soporte_id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':soporte_id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar soporte de factura (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId_factura(): ?int
    {
        return $this->id_factura;
    }

    public function setId_factura(?int $id_factura): self
    {
        $this->id_factura = $id_factura;
        return $this;
    }

    public function getNombre_archivo(): ?string
    {
        return $this->nombre_archivo;
    }

    public function setNombre_archivo(?string $nombre_archivo): self
    {
        $this->nombre_archivo = $nombre_archivo;
        return $this;
    }

    // --- Métodos de relación ---

    /**
     * Obtiene el objeto Factura asociado a este soporte.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return Factura|null Objeto Factura o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public function getFactura(PDO $conexion): ?Factura
    {
        if ($this->id_factura === null) {
            return null;
        }

        return Factura::get($this->id_factura, $conexion);
    }

    // --- Métodos de utilidad ---

    /**
     * Obtiene la extensión del archivo.
     *
     * @return string|null Extensión del archivo o null si no tiene nombre.
     */
    public function getExtensionArchivo(): ?string
    {
        if ($this->nombre_archivo === null) {
            return null;
        }

        $extension = pathinfo($this->nombre_archivo, PATHINFO_EXTENSION);
        return $extension ?: null;
    }

    /**
     * Obtiene el nombre del archivo sin la extensión.
     *
     * @return string|null Nombre del archivo sin extensión o null si no tiene nombre.
     */
    public function getNombreArchivoSinExtension(): ?string
    {
        if ($this->nombre_archivo === null) {
            return null;
        }

        return pathinfo($this->nombre_archivo, PATHINFO_FILENAME);
    }

    /**
     * Verifica si el archivo es una imagen basándose en su extensión.
     *
     * @return bool True si es una imagen, False en caso contrario.
     */
    public function esImagen(): bool
    {
        $extension = $this->getExtensionArchivo();
        if ($extension === null) {
            return false;
        }

        $extensionesImagen = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        return in_array(strtolower($extension), $extensionesImagen);
    }

    /**
     * Verifica si el archivo es un PDF basándose en su extensión.
     *
     * @return bool True si es un PDF, False en caso contrario.
     */
    public function esPdf(): bool
    {
        $extension = $this->getExtensionArchivo();
        return $extension !== null && strtolower($extension) === 'pdf';
    }

    /**
     * Obtiene el tipo MIME aproximado basándose en la extensión del archivo.
     *
     * @return string|null Tipo MIME o null si no se puede determinar.
     */
    public function getTipoMime(): ?string
    {
        $extension = $this->getExtensionArchivo();
        if ($extension === null) {
            return null;
        }

        $tiposMime = [
            'pdf'  => 'application/pdf',
            'jpg'  => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png'  => 'image/png',
            'gif'  => 'image/gif',
            'bmp'  => 'image/bmp',
            'webp' => 'image/webp',
            'svg'  => 'image/svg+xml',
            'doc'  => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls'  => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'txt'  => 'text/plain',
            'zip'  => 'application/zip',
            'rar'  => 'application/x-rar-compressed'
        ];

        return $tiposMime[strtolower($extension)] ?? 'application/octet-stream';
    }

    /**
     * Valida que los datos del soporte sean válidos.
     *
     * @return bool True si los datos son válidos.
     * @throws Exception Si los datos no son válidos.
     */
    public function validar(): bool
    {
        if (empty(trim($this->nombre_archivo))) {
            throw new Exception("El nombre del archivo no puede estar vacío.");
        }

        if ($this->id_factura === null || $this->id_factura <= 0) {
            throw new Exception("Debe especificar una factura válida.");
        }

        // Validar que el nombre del archivo no contenga caracteres peligrosos
        $caracteresProhibidos = ['/', '\\', '..', '<', '>', ':', '"', '|', '?', '*'];
        foreach ($caracteresProhibidos as $caracter) {
            if (strpos($this->nombre_archivo, $caracter) !== false) {
                throw new Exception("El nombre del archivo contiene caracteres no permitidos.");
            }
        }

        return true;
    }
}
